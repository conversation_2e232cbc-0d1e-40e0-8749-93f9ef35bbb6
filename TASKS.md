# MusicDou 项目开发任务清单

> **重要说明**: 此文档用于跟踪项目开发进度，每完成一个任务请更新对应状态。
> 状态说明：`[ ]` 未开始，`[/]` 进行中，`[x]` 已完成，`[-]` 已取消

## 第一阶段：基础环境搭建 (预计2-3天)

### [x] 1.1 项目初始化
**完成时间**: 2025-07-29
**详细描述**: 创建Node.js项目基础结构，配置开发环境
**具体任务**:
- [x] 初始化npm项目 (`npm init`)
- [x] 创建基础目录结构 (src/, tests/, docs/)
- [x] 配置ESLint和Prettier代码规范
- [x] 配置nodemon开发环境
- [x] 创建.gitignore文件
- [x] 设置环境变量配置 (.env文件)
**备注**: 已安装Node.js v24.4.1和npm v11.4.2，创建了完整的项目结构和开发环境配置
**问题记录**: 系统初始未安装Node.js，使用Homebrew成功安装

### [x] 1.2 依赖包安装和配置
**完成时间**: 2025-07-29
**详细描述**: 安装项目所需的核心依赖包
**具体任务**:
- [x] 安装Express.js框架
- [x] 安装MongoDB相关包 (mongoose)
- [x] 安装Redis相关包 (redis, connect-redis, express-session)
- [x] 安装身份验证包 (jsonwebtoken, passport, passport-jwt, bcryptjs)
- [x] 安装文件处理包 (multer, minio)
- [x] 安装音频处理包 (fluent-ffmpeg, music-metadata)
- [x] 安装其他工具包 (cors, helmet, morgan, dotenv, express-rate-limit)
**备注**: 已安装所有核心依赖包，使用fluent-ffmpeg和music-metadata替代node-ffmpeg
**问题记录**: FFmpeg系统安装失败(网络问题)，后续需要单独安装FFmpeg系统依赖

### [x] 1.3 基础服务器搭建
**完成时间**: 2025-07-29
**详细描述**: 创建Express服务器和基础中间件配置
**具体任务**:
- [x] 创建app.js主文件
- [x] 配置Express基础中间件 (cors, helmet, morgan)
- [x] 设置路由基础结构
- [x] 配置错误处理中间件
- [x] 创建健康检查接口 (/health)
- [x] 创建数据库配置文件
- [x] 创建MinIO配置文件
**备注**: 服务器成功启动，健康检查和API接口测试通过，降级Express到4.x版本解决兼容性问题
**问题记录**: Express 5.x版本存在路径解析问题，已降级到4.18.0版本

### [x] 1.4 数据库连接配置
**完成时间**: 2025-07-29
**详细描述**: 配置MongoDB和Redis数据库连接
**具体任务**:
- [x] 配置MongoDB连接 (mongoose)
- [x] 配置Redis连接
- [x] 创建数据库连接工具函数
- [x] 设置数据库连接错误处理
- [x] 测试数据库连接
- [x] 安装MongoDB和Redis服务
- [x] 启动数据库服务
- [x] **Docker迁移**: 卸载本地MongoDB和Redis服务
- [x] **Docker迁移**: 创建Docker Compose配置
- [x] **Docker迁移**: 更新环境变量配置
**备注**: MongoDB和Redis连接成功，服务正常运行，已清理连接警告。**重要更新**: 已将MongoDB、Redis和MinIO服务迁移到Docker容器中运行，创建了完整的Docker开发环境配置
**问题记录**: 用户要求将所有服务迁移到Docker容器，已成功卸载本地服务并配置Docker环境

### [/] 1.5 MinIO对象存储配置
**开始时间**: 2025-07-29
**详细描述**: 配置MinIO客户端和存储桶 (Docker环境)
**具体任务**:
- [x] **Docker迁移**: 配置MinIO Docker容器
- [x] **Docker迁移**: 创建MinIO配置文件 (src/config/minio.js)
- [x] **Docker迁移**: 更新环境变量配置
- [ ] 启动Docker服务并测试MinIO连接
- [ ] 创建存储桶 (music, images, avatars)
- [ ] 配置文件上传中间件
- [ ] 创建文件访问URL生成工具
- [ ] 测试文件上传和下载功能
**当前状态**: Docker配置已完成，等待Docker Desktop安装完成后启动服务
**问题记录**: Docker Desktop安装正在进行中，需要等待安装完成后继续

## 第二阶段：用户系统开发 (预计3-4天)

### [ ] 2.1 用户数据模型设计
**详细描述**: 创建用户相关的数据库模型
**具体任务**:
- [ ] 创建User模型 (models/User.js)
- [ ] 定义用户字段 (username, email, password, userGroup, points等)
- [ ] 添加密码加密中间件
- [ ] 创建用户索引 (email, username唯一索引)
- [ ] 添加用户模型验证规则

### [ ] 2.2 用户注册功能
**详细描述**: 实现用户注册API和业务逻辑
**具体任务**:
- [ ] 创建注册路由 (POST /api/v1/auth/register)
- [ ] 实现注册控制器 (controllers/authController.js)
- [ ] 添加邮箱和用户名重复检查
- [ ] 实现密码加密存储
- [ ] 创建默认歌单逻辑
- [ ] 添加注册成功积分奖励

### [ ] 2.3 用户登录功能
**详细描述**: 实现用户登录API和JWT认证
**具体任务**:
- [ ] 创建登录路由 (POST /api/v1/auth/login)
- [ ] 实现登录控制器
- [ ] 配置JWT策略 (passport-jwt)
- [ ] 实现JWT token生成
- [ ] 添加登录失败次数限制
- [ ] 更新最后登录时间

### [ ] 2.4 用户认证中间件
**详细描述**: 创建JWT认证和权限检查中间件
**具体任务**:
- [ ] 创建JWT认证中间件 (middleware/auth.js)
- [ ] 创建权限检查中间件 (admin, vip权限)
- [ ] 实现token刷新机制
- [ ] 添加token黑名单功能
- [ ] 创建登出功能

### [ ] 2.5 用户信息管理
**详细描述**: 实现用户信息查看和修改功能
**具体任务**:
- [ ] 创建获取用户信息接口 (GET /api/v1/users/profile)
- [ ] 创建更新用户信息接口 (PUT /api/v1/users/profile)
- [ ] 实现密码修改功能
- [ ] 创建用户头像上传接口 (POST /api/v1/upload/avatar)
- [ ] 添加用户信息验证

## 第三阶段：积分系统开发 (预计2天)

### [ ] 3.1 积分系统数据模型
**详细描述**: 设计积分记录和规则数据模型
**具体任务**:
- [ ] 创建PointRecord模型 (积分记录)
- [ ] 定义积分类型 (注册、签到、上传、分享等)
- [ ] 创建积分规则配置
- [ ] 添加积分统计字段
- [ ] 设置积分记录索引

### [ ] 3.2 积分获取功能
**详细描述**: 实现各种积分获取场景
**具体任务**:
- [ ] 实现注册积分奖励
- [ ] 实现每日签到积分
- [ ] 实现上传音乐积分奖励
- [ ] 实现分享歌单积分奖励
- [ ] 创建积分记录服务 (services/pointService.js)

### [ ] 3.3 积分消费功能
**详细描述**: 实现积分兑换和消费功能
**具体任务**:
- [ ] 创建积分兑换VIP接口
- [ ] 实现积分商城基础功能
- [ ] 添加积分余额检查
- [ ] 创建积分消费记录
- [ ] 实现积分历史查询接口

## 第四阶段：音乐管理系统 (预计4-5天)

### [ ] 4.1 音乐数据模型设计
**详细描述**: 创建音乐文件相关数据模型
**具体任务**:
- [ ] 创建Music模型 (models/Music.js)
- [ ] 定义音乐字段 (title, artist, album, duration, bitrate等)
- [ ] 添加音乐文件路径和MinIO信息
- [ ] 创建音乐索引 (标题、艺术家搜索)
- [ ] 添加音乐状态管理 (pending, approved, rejected)

### [ ] 4.2 音乐上传功能
**详细描述**: 实现音乐文件上传和处理
**具体任务**:
- [ ] 创建音乐上传接口 (POST /api/v1/music)
- [ ] 配置multer文件上传中间件
- [ ] 实现文件格式验证 (MP3, FLAC, WAV, AAC)
- [ ] 添加文件大小限制
- [ ] 实现文件上传到MinIO

### [ ] 4.3 音频质量检测
**详细描述**: 使用FFmpeg分析音频文件质量
**具体任务**:
- [ ] 集成FFmpeg音频分析
- [ ] 实现比特率检测 (128k, 192k, 320k, 无损)
- [ ] 实现采样率检测
- [ ] 获取音频时长信息
- [ ] 提取音频元数据 (ID3标签)

### [ ] 4.4 音乐元数据处理
**详细描述**: 提取和处理音乐文件元数据
**具体任务**:
- [ ] 实现ID3标签读取
- [ ] 提取歌曲名称、艺术家、专辑信息
- [ ] 提取专辑封面图片
- [ ] 处理中文编码问题
- [ ] 创建元数据更新接口

### [ ] 4.5 音乐管理接口
**详细描述**: 实现音乐的CRUD操作接口
**具体任务**:
- [ ] 创建音乐列表接口 (GET /api/v1/music)
- [ ] 创建音乐详情接口 (GET /api/v1/music/:id)
- [ ] 创建音乐更新接口 (PUT /api/v1/music/:id)
- [ ] 创建音乐删除接口 (DELETE /api/v1/music/:id)
- [ ] 实现音乐审核功能 (管理员)

## 第五阶段：歌单系统开发 (预计3-4天)

### [ ] 5.1 歌单数据模型设计
**详细描述**: 创建歌单相关数据模型
**具体任务**:
- [ ] 创建Playlist模型 (models/Playlist.js)
- [ ] 定义歌单字段 (name, description, coverImage, songs等)
- [ ] 添加歌单权限控制 (public/private)
- [ ] 创建默认歌单标识
- [ ] 设置歌单和用户关联

### [ ] 5.2 歌单基础功能
**详细描述**: 实现歌单的创建、编辑、删除功能
**具体任务**:
- [ ] 创建歌单创建接口 (POST /api/v1/playlists)
- [ ] 创建歌单列表接口 (GET /api/v1/playlists)
- [ ] 创建歌单详情接口 (GET /api/v1/playlists/:id)
- [ ] 创建歌单更新接口 (PUT /api/v1/playlists/:id)
- [ ] 创建歌单删除接口 (DELETE /api/v1/playlists/:id)

### [ ] 5.3 歌单歌曲管理
**详细描述**: 实现歌单内歌曲的添加、删除、排序功能
**具体任务**:
- [ ] 创建添加歌曲到歌单接口 (POST /api/v1/playlists/:id/songs)
- [ ] 创建从歌单移除歌曲接口 (DELETE /api/v1/playlists/:id/songs/:songId)
- [ ] 实现歌单内歌曲排序功能
- [ ] 添加重复歌曲检查
- [ ] 实现批量添加歌曲功能

### [ ] 5.4 歌单收藏功能
**详细描述**: 实现用户收藏其他用户公开歌单功能
**具体任务**:
- [ ] 创建收藏歌单接口 (POST /api/v1/playlists/:id/favorite)
- [ ] 创建取消收藏接口 (DELETE /api/v1/playlists/:id/favorite)
- [ ] 创建我收藏的歌单列表接口
- [ ] 添加收藏数量统计
- [ ] 实现收藏状态查询

### [ ] 5.5 封面上传功能
**详细描述**: 实现歌单封面图片上传功能
**具体任务**:
- [ ] 创建封面上传接口 (POST /api/v1/upload/cover)
- [ ] 配置图片文件验证 (JPG, PNG, WEBP)
- [ ] 实现图片尺寸和大小限制
- [ ] 添加图片压缩功能
- [ ] 实现封面图片更新

## 第六阶段：首页推荐系统 (预计2-3天)

### [ ] 6.1 推荐算法设计
**详细描述**: 设计歌单推荐和热门排行算法
**具体任务**:
- [ ] 设计热门歌单算法 (播放量、收藏量、时间权重)
- [ ] 设计推荐歌单算法 (用户偏好、协同过滤)
- [ ] 创建歌单统计数据模型
- [ ] 实现播放量统计
- [ ] 添加推荐权重计算

### [ ] 6.2 首页接口开发
**详细描述**: 实现登录后首页的推荐和热门歌单接口
**具体任务**:
- [ ] 创建推荐歌单接口 (GET /api/v1/home/<USER>
- [ ] 创建热门歌单接口 (GET /api/v1/home/<USER>
- [ ] 实现分页功能
- [ ] 添加缓存机制 (Redis)
- [ ] 创建首页数据聚合接口

### [ ] 6.3 统计数据收集
**详细描述**: 实现用户行为数据收集用于推荐
**具体任务**:
- [ ] 创建播放记录模型
- [ ] 实现播放量统计
- [ ] 收集用户偏好数据
- [ ] 创建行为分析服务
- [ ] 实现数据定时更新任务

## 第七阶段：搜索系统开发 (预计3-4天)

### [ ] 7.1 搜索插件架构设计
**详细描述**: 设计可扩展的音乐平台搜索插件系统
**具体任务**:
- [ ] 设计插件接口规范
- [ ] 创建插件管理器 (services/pluginManager.js)
- [ ] 实现插件动态加载机制
- [ ] 创建插件配置管理
- [ ] 添加插件状态监控

### [ ] 7.2 搜索插件开发
**详细描述**: 开发各音乐平台的搜索插件
**具体任务**:
- [ ] 开发网易云音乐搜索插件 (plugins/netease.js)
- [ ] 开发QQ音乐搜索插件 (plugins/qq.js)
- [ ] 开发酷狗音乐搜索插件 (plugins/kugou.js)
- [ ] 开发酷我音乐搜索插件 (plugins/kuwo.js)
- [ ] 实现本站音乐搜索

### [ ] 7.3 搜索接口开发
**详细描述**: 实现统一的搜索接口和结果格式化
**具体任务**:
- [ ] 创建搜索接口 (GET /api/v1/search)
- [ ] 实现多平台搜索结果聚合
- [ ] 统一搜索结果格式
- [ ] 添加搜索结果缓存
- [ ] 实现搜索历史记录

### [ ] 7.4 高级搜索功能
**详细描述**: 实现高级搜索和过滤功能
**具体任务**:
- [ ] 实现按艺术家搜索
- [ ] 实现按专辑搜索
- [ ] 实现按歌词搜索
- [ ] 添加搜索结果排序
- [ ] 实现搜索建议功能

## 第八阶段：链接解析系统 (预计2-3天)

### [ ] 8.1 URL识别系统
**详细描述**: 实现自动识别音乐平台URL的功能
**具体任务**:
- [ ] 创建URL模式匹配规则
- [ ] 实现平台自动识别
- [ ] 添加URL验证功能
- [ ] 创建解析结果缓存
- [ ] 实现解析失败处理

### [ ] 8.2 解析插件开发
**详细描述**: 开发各平台的链接解析插件
**具体任务**:
- [ ] 开发网易云音乐解析插件
- [ ] 开发QQ音乐解析插件
- [ ] 开发酷狗音乐解析插件
- [ ] 开发酷我音乐解析插件
- [ ] 实现解析结果标准化

### [ ] 8.3 解析接口开发
**详细描述**: 实现链接解析API接口
**具体任务**:
- [ ] 创建解析接口 (POST /api/v1/parse-url)
- [ ] 实现解析结果返回
- [ ] 添加解析限流保护
- [ ] 实现解析历史记录
- [ ] 创建批量解析功能

## 第九阶段：测试和优化 (预计3-4天)

### [ ] 9.1 单元测试
**详细描述**: 编写核心功能的单元测试
**具体任务**:
- [ ] 配置测试环境 (Jest/Mocha)
- [ ] 编写用户模块测试
- [ ] 编写音乐模块测试
- [ ] 编写歌单模块测试
- [ ] 编写搜索模块测试

### [ ] 9.2 集成测试
**详细描述**: 编写API接口的集成测试
**具体任务**:
- [ ] 配置测试数据库
- [ ] 编写API接口测试
- [ ] 测试文件上传功能
- [ ] 测试认证和权限
- [ ] 测试插件系统

### [ ] 9.3 性能优化
**详细描述**: 优化系统性能和响应速度
**具体任务**:
- [ ] 添加Redis缓存
- [ ] 优化数据库查询
- [ ] 实现API响应压缩
- [ ] 优化文件上传性能
- [ ] 添加请求限流

### [ ] 9.4 安全加固
**详细描述**: 加强系统安全防护
**具体任务**:
- [ ] 实现API请求限流
- [ ] 添加XSS防护
- [ ] 加强文件上传安全检查
- [ ] 实现SQL注入防护
- [ ] 配置HTTPS和安全头

## 第十阶段：部署和文档 (预计2天)

### [ ] 10.1 部署准备
**详细描述**: 准备生产环境部署
**具体任务**:
- [ ] 配置生产环境变量
- [ ] 创建Docker配置文件
- [ ] 配置PM2进程管理
- [ ] 设置日志管理
- [ ] 配置监控告警

### [ ] 10.2 API文档
**详细描述**: 完善API接口文档
**具体任务**:
- [ ] 使用Swagger生成API文档
- [ ] 编写接口使用示例
- [ ] 创建错误码说明
- [ ] 添加认证说明
- [ ] 编写部署指南

---

## 任务更新说明

**重要**: 每完成一个任务，请按以下格式更新：
1. 将任务状态从 `[ ]` 改为 `[x]`
2. 在任务下方添加完成时间和备注
3. 如遇到问题，详细记录解决方案

**示例**:
```markdown
### [x] 1.1 项目初始化
**完成时间**: 2024-01-15
**备注**: 已完成基础目录结构创建，配置了ESLint和Prettier
**问题记录**: 无
```

**下一个agent继续工作的指引**:
1. 查看当前任务状态，找到最后一个完成的任务
2. 从下一个未开始的任务继续工作
3. 严格按照任务描述和具体要求执行
4. 完成后及时更新任务状态和备注

---

## 🐳 Docker环境配置说明 (2025-07-29更新)

### 当前状态
- **Docker Desktop**: 正在安装中 (通过Homebrew)
- **服务迁移**: 已完成MongoDB、Redis、MinIO从本地到Docker的迁移配置
- **配置文件**: 所有Docker相关配置文件已创建完成

### 已完成的Docker配置

#### 1. Docker Compose配置 (`docker-compose.yml`)
- **MongoDB 7.0**: 端口27017，包含初始化脚本
- **Redis 7.2**: 端口6379，配置密码认证
- **MinIO**: 端口9000(API)/9001(Console)，对象存储服务
- **网络**: 统一的musicdou-network网络
- **数据持久化**: 所有服务数据通过Docker volumes持久化

#### 2. MongoDB初始化配置 (`docker/mongodb/init/init-db.js`)
- 自动创建musicdou数据库和应用用户
- 预创建基础集合 (users, music, playlists, uploads)
- 设置必要的数据库索引

#### 3. 环境变量更新 (`.env`)
- MongoDB连接字符串更新为Docker服务
- Redis密码配置
- MinIO访问密钥配置

#### 4. Docker管理脚本 (`scripts/docker-dev.sh`)
- 服务启动/停止/重启命令
- 日志查看和容器进入功能
- 数据清理和状态检查功能
- 已设置执行权限

#### 5. 测试脚本 (`scripts/test-docker.sh`)
- Docker环境完整性检查
- 端口占用检查
- 配置文件验证
- 已设置执行权限

#### 6. npm脚本更新 (`package.json`)
- 添加Docker相关的npm命令
- `npm run docker:start` - 启动服务
- `npm run docker:stop` - 停止服务
- `npm run docker:status` - 查看状态

#### 7. 文档创建 (`docs/DOCKER.md`)
- 完整的Docker使用说明
- 故障排除指南
- 开发工作流程

### 下一步操作 (继续工作的Agent需要执行)

#### 1. 完成Docker安装
```bash
# 检查Docker安装状态
brew list --cask | grep docker

# 如果安装完成，启动Docker Desktop
open /Applications/Docker.app
```

#### 2. 验证Docker环境
```bash
# 运行测试脚本
./scripts/test-docker.sh

# 或分步检查
./scripts/test-docker.sh docker
./scripts/test-docker.sh compose
```

#### 3. 启动Docker服务
```bash
# 启动所有服务
npm run docker:start

# 或使用脚本
./scripts/docker-dev.sh start
```

#### 4. 验证服务连接
```bash
# 检查服务状态
npm run docker:status

# 查看服务日志
npm run docker:logs
```

#### 5. 测试应用连接
```bash
# 启动应用程序
npm run dev

# 检查健康状态
curl http://localhost:3000/health
```

#### 6. 完成任务1.5
- 验证MinIO连接
- 创建存储桶
- 测试文件上传功能
- 更新任务状态为完成

### 重要提醒
1. **Docker Desktop必须先安装并启动**才能继续后续工作
2. **所有服务现在都在Docker容器中运行**，不再使用本地安装的服务
3. **数据库连接配置已更新**，使用Docker服务的连接参数
4. **MinIO配置文件已创建**，但需要Docker服务启动后才能测试
5. **开发环境现在完全容器化**，便于团队协作和部署

### 故障排除
- 如果Docker安装失败，可以手动下载Docker Desktop安装
- 如果端口冲突，检查本地是否还有其他服务占用相关端口
- 如果服务启动失败，查看Docker日志排查问题
- 详细故障排除请参考 `docs/DOCKER.md`
