# MusicDou - 音乐站点后端系统

## 项目概述

MusicDou 是一个基于 Node.js 开发的音乐站点后端系统，采用单体架构设计，提供完整的音乐管理、用户管理、歌单管理和第三方平台集成功能。

## 核心功能

### 1. 用户管理系统

#### 用户功能
- 用户注册/登录/注销
- 用户资料管理
- 密码修改和找回
- 用户头像上传

#### 用户组和权限
- **管理员组**: 系统管理权限，用户管理，内容审核
- **VIP用户组**: 高级功能权限，无广告，高品质音乐下载
- **普通用户组**: 基础功能权限

> 注意：系统不设置访客组，所有用户必须登录后才能访问任何功能接口

#### 用户积分系统
- 注册奖励积分
- 每日签到积分
- 上传音乐奖励积分
- 分享歌单奖励积分
- 积分兑换VIP时长
- 积分商城功能

### 2. 音乐管理系统

#### 音乐上传功能
- 支持多种音频格式 (MP3, FLAC, WAV, AAC)
- 自动音质检测和分析
  - 比特率检测 (128k, 192k, 320k, 无损等)
  - 采样率检测
  - 音频时长获取
  - 文件大小限制
- 音乐元数据提取
  - 歌曲名称、艺术家、专辑
  - 封面图片提取
  - 发行年份、流派等信息
- 音乐文件存储 (MinIO对象存储)

#### 音乐质量管理
- 音质等级分类
  - 标准品质 (128kbps)
  - 高品质 (192kbps)
  - 超高品质 (320kbps)
  - 无损品质 (FLAC)
- 重复音乐检测和去重
- 音乐审核机制

### 3. 歌单管理系统

#### 歌单功能
- 创建/编辑/删除歌单
- 歌单封面设置
- 歌单描述和标签
- 歌单公开/私有设置
- 歌单分享功能

#### 歌曲管理
- 添加歌曲到歌单
- 从歌单移除歌曲
- 歌单内歌曲排序
- 歌曲可加入多个歌单
- 默认歌单自动创建

#### 歌单分类
- **我创建的歌单**: 用户自己创建的歌单列表
- **我收藏的歌单**: 用户收藏的其他用户公开歌单

> 注意：推荐歌单和热门歌单将在登录后的首页展示，不在用户歌单管理页面

### 4. 链接解析系统

#### 插件化架构
- 支持动态加载解析插件
- 统一的插件接口规范
- 插件配置管理
- 插件状态监控

#### 支持平台
- 网易云音乐
- QQ音乐
- 酷狗音乐
- 酷我音乐
- 咪咕音乐
- 可扩展其他平台

#### 解析功能
- URL自动识别平台
- 歌曲信息解析
- 播放链接获取
- 歌词信息获取
- 专辑封面获取

### 5. 搜索系统

#### 多平台搜索
- 分栏式搜索界面
- 支持平台：
  - 网易云音乐
  - QQ音乐
  - 酷狗音乐
  - 酷我音乐
  - 本站音乐
- 搜索结果统一格式化

### 6. 文件上传系统

#### 封面上传功能
- 歌单封面上传
- 用户头像上传
- 音乐专辑封面上传
- 图片格式支持 (JPG, PNG, WEBP)
- 图片尺寸和大小限制
- 自动图片压缩和优化

#### MinIO对象存储
- 所有文件统一存储在MinIO
- 音乐文件存储
- 图片文件存储
- 文件访问权限控制
- 存储桶分类管理

#### 搜索功能
- 歌曲名搜索
- 艺术家搜索
- 专辑搜索
- 歌词搜索
- 高级搜索过滤

## 技术架构

### 后端技术栈
- **运行环境**: Node.js 18+
- **Web框架**: Express.js
- **数据库**: MongoDB + Redis
- **ORM**: Mongoose
- **身份验证**: JWT + Passport.js
- **文件存储**: MinIO对象存储
- **音频处理**: FFmpeg + node-ffmpeg

### API设计规范
- RESTful API风格
- 统一的响应格式
- API版本控制 (v1, v2)
- 请求限流和防护
- API文档自动生成

#### API命名规范
```
GET    /api/v1/users              # 获取用户列表
POST   /api/v1/users              # 创建用户
GET    /api/v1/users/:id          # 获取用户详情
PUT    /api/v1/users/:id          # 更新用户信息
DELETE /api/v1/users/:id          # 删除用户

GET    /api/v1/music              # 获取音乐列表
POST   /api/v1/music              # 上传音乐
GET    /api/v1/music/:id          # 获取音乐详情
PUT    /api/v1/music/:id          # 更新音乐信息
DELETE /api/v1/music/:id          # 删除音乐

GET    /api/v1/playlists          # 获取歌单列表
POST   /api/v1/playlists          # 创建歌单
GET    /api/v1/playlists/:id      # 获取歌单详情
PUT    /api/v1/playlists/:id      # 更新歌单
DELETE /api/v1/playlists/:id      # 删除歌单

POST   /api/v1/parse-url          # 解析音乐链接
GET    /api/v1/search             # 搜索音乐

POST   /api/v1/upload/cover       # 上传封面图片
POST   /api/v1/upload/avatar      # 上传用户头像
GET    /api/v1/home/<USER>
GET    /api/v1/home/<USER>
```

### 数据库设计

#### 用户表 (users)
```javascript
{
  _id: ObjectId,
  username: String,
  email: String,
  password: String, // 加密存储
  avatar: String,
  userGroup: String, // admin, vip, normal
  points: Number,
  createdAt: Date,
  updatedAt: Date,
  lastLoginAt: Date
}
```

#### 音乐表 (music)
```javascript
{
  _id: ObjectId,
  title: String,
  artist: String,
  album: String,
  duration: Number,
  bitrate: Number,
  sampleRate: Number,
  fileSize: Number,
  filePath: String,
  coverImage: String,
  uploadedBy: ObjectId, // 用户ID
  quality: String, // standard, high, super, lossless
  status: String, // pending, approved, rejected
  createdAt: Date
}
```

#### 歌单表 (playlists)
```javascript
{
  _id: ObjectId,
  name: String,
  description: String,
  coverImage: String,
  isPublic: Boolean,
  isDefault: Boolean,
  createdBy: ObjectId, // 用户ID
  songs: [ObjectId], // 音乐ID数组
  tags: [String],
  playCount: Number,
  createdAt: Date,
  updatedAt: Date
}
```

## 项目结构

```
musicdou/
├── src/
│   ├── controllers/     # 控制器
│   ├── models/         # 数据模型
│   ├── routes/         # 路由定义
│   ├── middleware/     # 中间件
│   ├── services/       # 业务逻辑
│   ├── plugins/        # 解析插件
│   ├── utils/          # 工具函数
│   └── config/         # 配置文件
├── tests/              # 测试文件
├── docs/               # 文档
├── package.json
└── README.md

注意：不再需要本地uploads目录，所有文件都存储在MinIO中
```

## 开发计划

### 第一阶段：基础功能
- [ ] 项目初始化和环境搭建
- [ ] 用户注册/登录系统
- [ ] 基础API框架搭建
- [ ] 数据库设计和模型创建

### 第二阶段：核心功能
- [ ] 音乐上传和管理
- [ ] 歌单创建和管理
- [ ] 用户权限系统
- [ ] 积分系统实现

### 第三阶段：高级功能
- [ ] 链接解析插件系统
- [ ] 多平台搜索功能
- [ ] 音质检测和分析
- [ ] 性能优化和缓存

### 第四阶段：完善和部署
- [ ] 单元测试和集成测试
- [ ] API文档完善
- [ ] 部署和监控
- [ ] 安全加固

## 安全考虑

- 用户密码加密存储
- JWT Token安全管理
- API请求限流
- 文件上传安全检查
- SQL注入防护
- XSS攻击防护
- CORS配置

## 性能优化

- Redis缓存热点数据
- 数据库索引优化
- MinIO文件访问优化
- 图片压缩和懒加载
- API响应压缩
- 数据库连接池

## 监控和日志

- 系统性能监控
- 错误日志记录
- 用户行为分析
- API调用统计
- 服务器资源监控
