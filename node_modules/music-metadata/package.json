{"name": "music-metadata", "description": "Music metadata parser for Node.js, supporting virtual any audio and tag format.", "version": "11.7.3", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Borewit"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/Borewit"}, {"type": "buymeacoffee", "url": "https://buymeacoffee.com/borewit"}], "sideEffects": false, "type": "module", "exports": {"node": {"import": "./lib/index.js", "module-sync": "./lib/index.js", "types": "./lib/index.d.ts"}, "default": {"import": "./lib/core.js", "module-sync": "./lib/core.js", "types": "./lib/core.d.ts"}}, "types": "lib/index.d.ts", "files": ["lib/**/*.js", "lib/**/*.d.ts"], "keywords": ["music", "metadata", "meta", "audio", "tag", "tags", "duration", "MusicBrainz", "Discogs", "Picard", "ID3", "ID3v1", "ID3v2", "m4a", "m4b", "mp3", "mp4", "Vorbis", "ogg", "flac", "<PERSON><PERSON><PERSON>", "WebM", "EBML", "asf", "wma", "wmv", "ape", "MonkeyAudio", "aiff", "wav", "WavPack", "Opus", "speex", "musepack", "mpc", "dsd", "dsf", "mpc", "dff", "dsdiff", "aac", "adts", "length", "chapter", "info", "parse", "parser", "bwf", "slt", "lyrics"], "scripts": {"clean": "del-cli 'lib/**/*.js' 'lib/**/*.js.map' 'lib/**/*.d.ts' 'test/**/*.js' 'test/**/*.js.map' 'test/**/*.js' 'test/**/*.js.map' 'doc-gen/**/*.js' 'doc-gen/**/*.js.map'", "compile-src": "tsc -p lib --sourceMap false", "compile-test": "tsc -p test", "compile-doc": "tsc -p doc-gen", "compile": "yarn run compile-src && yarn compile-test && yarn compile-doc", "lint:ts": "biome check", "lint:md": "yarn run remark -u remark-preset-lint-consistent .", "lint": "yarn run lint:ts && yarn run lint:md", "test": "mocha", "build": "yarn run clean && yarn compile && yarn run doc-gen", "prepublishOnly": "yarn run build", "test-coverage": "c8 yarn run test", "send-codacy": "c8 report --reporter=text-lcov | codacy-coverage", "doc-gen": "yarn node doc-gen/gen.js", "typecheck": "tsc --project ./lib/tsconfig.json --noEmit && tsc --project ./test/tsconfig.json --noEmit", "update-biome": "yarn add -D --exact @biomejs/biome && npx @biomejs/biome migrate --write"}, "dependencies": {"@tokenizer/token": "^0.3.0", "content-type": "^1.0.5", "debug": "^4.4.1", "file-type": "^21.0.0", "media-typer": "^1.1.0", "strtok3": "^10.3.4", "token-types": "^6.0.4", "uint8array-extras": "^1.4.0"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@types/chai": "^5.2.2", "@types/chai-as-promised": "^8.0.2", "@types/content-type": "^1.1.9", "@types/debug": "^4.1.12", "@types/media-typer": "^1.1.3", "@types/mocha": "^10.0.10", "@types/node": "^24.1.0", "c8": "^10.1.3", "chai": "^5.2.1", "chai-as-promised": "^8.0.1", "del-cli": "^6.0.0", "mime": "^4.0.7", "mocha": "^11.7.1", "node-readable-to-web-readable-stream": "^0.4.2", "remark-cli": "^12.0.1", "remark-preset-lint-consistent": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18"}, "repository": "github:Borewit/music-metadata", "license": "MIT", "bugs": {"url": "https://github.com/Borewit/music-metadata/issues"}, "packageManager": "yarn@4.9.2"}