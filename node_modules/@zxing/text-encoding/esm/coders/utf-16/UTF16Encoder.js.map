{"version": 3, "file": "UTF16Encoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/utf-16/UTF16Encoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AACnD,OAAO,EAAE,sBAAsB,EAAE,MAAM,yBAAyB,CAAC;AAEjE;;;;;GAKG;AACH;IAIE,sBAAoB,QAAiB,EAAE,OAA4B;QAA/C,aAAQ,GAAR,QAAQ,CAAS;QACnC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,8BAAO,GAAP,UAAQ,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,aAAa;YAC9B,OAAO,QAAQ,CAAC;QAElB,gEAAgE;QAChE,4DAA4D;QAC5D,qCAAqC;QACrC,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;YACrC,OAAO,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE3D,0DAA0D;QAC1D,kDAAkD;QAClD,IAAM,IAAI,GAAG,sBAAsB,CACjC,CAAC,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE1D,6DAA6D;QAC7D,kDAAkD;QAClD,IAAM,KAAK,GAAG,sBAAsB,CAClC,CAAC,CAAC,UAAU,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE5D,uDAAuD;QACvD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IACH,mBAAC;AAAD,CAAC,AArCD,IAqCC"}