"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
class Credentials {
  constructor({
    accessKey,
    secretKey,
    sessionToken
  }) {
    this.accessKey = accessKey;
    this.secretKey = secretKey;
    this.sessionToken = sessionToken;
  }
  setAccessKey(accessKey) {
    this.accessKey = accessKey;
  }
  getAccessKey() {
    return this.accessKey;
  }
  setSecretKey(secretKey) {
    this.secretKey = secretKey;
  }
  getSecretKey() {
    return this.secretKey;
  }
  setSessionToken(sessionToken) {
    this.sessionToken = sessionToken;
  }
  getSessionToken() {
    return this.sessionToken;
  }
  get() {
    return this;
  }
}

// deprecated default export, please use named exports.
// keep for backward compatibility.
// eslint-disable-next-line import/no-default-export
exports.Credentials = Credentials;
var _default = Credentials;
exports.default = _default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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