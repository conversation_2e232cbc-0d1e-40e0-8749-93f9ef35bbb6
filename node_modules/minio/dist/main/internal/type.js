"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
// nodejs IncomingHttpHeaders is Record<string, string | string[]>, but it's actually this:
let ENCRYPTION_TYPES = /*#__PURE__*/function (ENCRYPTION_TYPES) {
  ENCRYPTION_TYPES["SSEC"] = "SSE-C";
  ENCRYPTION_TYPES["KMS"] = "KMS";
  return ENCRYPTION_TYPES;
}({});
exports.ENCRYPTION_TYPES = ENCRYPTION_TYPES;
let RETENTION_MODES = /*#__PURE__*/function (RETENTION_MODES) {
  RETENTION_MODES["GOVERNANCE"] = "GOVERNANCE";
  RETENTION_MODES["COMPLIANCE"] = "COMPLIANCE";
  return RETENTION_MODES;
}({});
exports.RETENTION_MODES = RETENTION_MODES;
let RETENTION_VALIDITY_UNITS = /*#__PURE__*/function (RETENTION_VALIDITY_UNITS) {
  RETENTION_VALIDITY_UNITS["DAYS"] = "Days";
  RETENTION_VALIDITY_UNITS["YEARS"] = "Years";
  return RETENTION_VALIDITY_UNITS;
}({});
exports.RETENTION_VALIDITY_UNITS = RETENTION_VALIDITY_UNITS;
let LEGAL_HOLD_STATUS = /*#__PURE__*/function (LEGAL_HOLD_STATUS) {
  LEGAL_HOLD_STATUS["ENABLED"] = "ON";
  LEGAL_HOLD_STATUS["DISABLED"] = "OFF";
  return LEGAL_HOLD_STATUS;
}({});
/* Replication Config types */
/* Replication Config types */
/**
 * @deprecated keep for backward compatible, use `LEGAL_HOLD_STATUS` instead
 */
/** List object api types **/
// Common types
/** List object api types **/
exports.LEGAL_HOLD_STATUS = LEGAL_HOLD_STATUS;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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