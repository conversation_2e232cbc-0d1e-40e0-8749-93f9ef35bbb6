"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseBucketNotification = parseBucketNotification;
exports.parseListObjectsV2 = parseListObjectsV2;
exports.parseListObjectsV2WithMetadata = parseListObjectsV2WithMetadata;
var errors = _interopRequireWildcard(require("./errors.js"), true);
var _helper = require("./internal/helper.js");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/*
 * MinIO Javascript Library for Amazon S3 Compatible Cloud Storage, (C) 2015 MinIO, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// parse XML response for bucket notification
function parseBucketNotification(xml) {
  var result = {
    TopicConfiguration: [],
    QueueConfiguration: [],
    CloudFunctionConfiguration: []
  };
  // Parse the events list
  var genEvents = function (events) {
    var result = [];
    if (events) {
      (0, _helper.toArray)(events).forEach(s3event => {
        result.push(s3event);
      });
    }
    return result;
  };
  // Parse all filter rules
  var genFilterRules = function (filters) {
    var result = [];
    if (filters) {
      filters = (0, _helper.toArray)(filters);
      if (filters[0].S3Key) {
        filters[0].S3Key = (0, _helper.toArray)(filters[0].S3Key);
        if (filters[0].S3Key[0].FilterRule) {
          (0, _helper.toArray)(filters[0].S3Key[0].FilterRule).forEach(rule => {
            var Name = (0, _helper.toArray)(rule.Name)[0];
            var Value = (0, _helper.toArray)(rule.Value)[0];
            result.push({
              Name,
              Value
            });
          });
        }
      }
    }
    return result;
  };
  var xmlobj = (0, _helper.parseXml)(xml);
  xmlobj = xmlobj.NotificationConfiguration;

  // Parse all topic configurations in the xml
  if (xmlobj.TopicConfiguration) {
    (0, _helper.toArray)(xmlobj.TopicConfiguration).forEach(config => {
      var Id = (0, _helper.toArray)(config.Id)[0];
      var Topic = (0, _helper.toArray)(config.Topic)[0];
      var Event = genEvents(config.Event);
      var Filter = genFilterRules(config.Filter);
      result.TopicConfiguration.push({
        Id,
        Topic,
        Event,
        Filter
      });
    });
  }
  // Parse all topic configurations in the xml
  if (xmlobj.QueueConfiguration) {
    (0, _helper.toArray)(xmlobj.QueueConfiguration).forEach(config => {
      var Id = (0, _helper.toArray)(config.Id)[0];
      var Queue = (0, _helper.toArray)(config.Queue)[0];
      var Event = genEvents(config.Event);
      var Filter = genFilterRules(config.Filter);
      result.QueueConfiguration.push({
        Id,
        Queue,
        Event,
        Filter
      });
    });
  }
  // Parse all QueueConfiguration arrays
  if (xmlobj.CloudFunctionConfiguration) {
    (0, _helper.toArray)(xmlobj.CloudFunctionConfiguration).forEach(config => {
      var Id = (0, _helper.toArray)(config.Id)[0];
      var CloudFunction = (0, _helper.toArray)(config.CloudFunction)[0];
      var Event = genEvents(config.Event);
      var Filter = genFilterRules(config.Filter);
      result.CloudFunctionConfiguration.push({
        Id,
        CloudFunction,
        Event,
        Filter
      });
    });
  }
  return result;
}

// parse XML response for list objects v2 in a bucket
function parseListObjectsV2(xml) {
  var result = {
    objects: [],
    isTruncated: false
  };
  var xmlobj = (0, _helper.parseXml)(xml);
  if (!xmlobj.ListBucketResult) {
    throw new errors.InvalidXMLError('Missing tag: "ListBucketResult"');
  }
  xmlobj = xmlobj.ListBucketResult;
  if (xmlobj.IsTruncated) {
    result.isTruncated = xmlobj.IsTruncated;
  }
  if (xmlobj.NextContinuationToken) {
    result.nextContinuationToken = xmlobj.NextContinuationToken;
  }
  if (xmlobj.Contents) {
    (0, _helper.toArray)(xmlobj.Contents).forEach(content => {
      var name = (0, _helper.sanitizeObjectKey)((0, _helper.toArray)(content.Key)[0]);
      var lastModified = new Date(content.LastModified);
      var etag = (0, _helper.sanitizeETag)(content.ETag);
      var size = content.Size;
      result.objects.push({
        name,
        lastModified,
        etag,
        size
      });
    });
  }
  if (xmlobj.CommonPrefixes) {
    (0, _helper.toArray)(xmlobj.CommonPrefixes).forEach(commonPrefix => {
      result.objects.push({
        prefix: (0, _helper.sanitizeObjectKey)((0, _helper.toArray)(commonPrefix.Prefix)[0]),
        size: 0
      });
    });
  }
  return result;
}

// parse XML response for list objects v2 with metadata in a bucket
function parseListObjectsV2WithMetadata(xml) {
  var result = {
    objects: [],
    isTruncated: false
  };
  var xmlobj = (0, _helper.parseXml)(xml);
  if (!xmlobj.ListBucketResult) {
    throw new errors.InvalidXMLError('Missing tag: "ListBucketResult"');
  }
  xmlobj = xmlobj.ListBucketResult;
  if (xmlobj.IsTruncated) {
    result.isTruncated = xmlobj.IsTruncated;
  }
  if (xmlobj.NextContinuationToken) {
    result.nextContinuationToken = xmlobj.NextContinuationToken;
  }
  if (xmlobj.Contents) {
    (0, _helper.toArray)(xmlobj.Contents).forEach(content => {
      var name = (0, _helper.sanitizeObjectKey)(content.Key);
      var lastModified = new Date(content.LastModified);
      var etag = (0, _helper.sanitizeETag)(content.ETag);
      var size = content.Size;
      var metadata;
      if (content.UserMetadata != null) {
        metadata = (0, _helper.toArray)(content.UserMetadata)[0];
      } else {
        metadata = null;
      }
      result.objects.push({
        name,
        lastModified,
        etag,
        size,
        metadata
      });
    });
  }
  if (xmlobj.CommonPrefixes) {
    (0, _helper.toArray)(xmlobj.CommonPrefixes).forEach(commonPrefix => {
      result.objects.push({
        prefix: (0, _helper.sanitizeObjectKey)((0, _helper.toArray)(commonPrefix.Prefix)[0]),
        size: 0
      });
    });
  }
  return result;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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